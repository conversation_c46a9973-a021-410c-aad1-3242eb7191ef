/* Exercise List Specific Styles */

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: var(--border-radius-lg);
  color: white;
}

.page-title-container {
  flex: 1;
}

.page-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-icon {
  font-size: 1.5rem;
}

.page-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .page-actions {
    width: 100%;
    justify-content: stretch;
  }

  .page-actions .modern-btn {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: 1.5rem;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* Filters Section */
.filters-section {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filters-section .form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.filters-section .form-select,
.filters-section .form-control {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.filters-section .form-select:focus,
.filters-section .form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.filters-section .input-group-text {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* Dark mode adjustments for filters */
[data-theme="dark"] .filters-section {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .filters-section .form-select,
[data-theme="dark"] .filters-section .form-control {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .filters-section .input-group-text {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* Exercise Grid Container */
.exercises-container {
  margin-bottom: var(--spacing-lg);
}

/* Exercise Card Styles */
.exercise-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) var(--transition-timing);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.exercise-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary);
}

/* Exercise Type Badge */
.exercise-type-badge {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  z-index: 2;
}

.exercise-type-badge .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Exercise Header */
.exercise-header {
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.exercise-header-content {
  padding-right: 4rem; /* Space for type badge */
}

.exercise-name {
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.exercise-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.category-badge {
  background: var(--primary-light);
  color: var(--primary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

.difficulty-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Exercise Content */
.exercise-content {
  padding: var(--spacing-md);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.exercise-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.exercise-details {
  margin-bottom: var(--spacing-sm);
}

.detail-item {
  font-size: 0.85rem;
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
}

.detail-item strong {
  color: var(--text-primary);
  font-weight: 600;
}

.exercise-instructions {
  margin-top: auto;
}

.exercise-instructions strong {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.85rem;
}

.instructions-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
  line-height: 1.4;
}

/* Exercise Actions */
.exercise-actions {
  padding: var(--spacing-sm) var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
  background: var(--bg-secondary);
}

.exercise-actions .btn {
  border-radius: var(--border-radius-sm);
  padding: 0.375rem 0.75rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .exercise-name {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .exercise-header {
    padding: var(--spacing-md) var(--spacing-sm) var(--spacing-xs);
  }

  .exercise-content {
    padding: var(--spacing-sm);
  }

  .exercise-actions {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .exercise-header-content {
    padding-right: 3rem;
  }
}

/* Dark mode adjustments */
[data-theme="dark"] .exercise-card {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-card:hover {
  border-color: var(--primary);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .exercise-header {
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-actions {
  border-color: var(--border-color);
  background: var(--bg-tertiary);
}

[data-theme="dark"] .category-badge {
  background: var(--primary-dark);
  color: var(--primary-light);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  margin: var(--spacing-lg) 0;
}

.empty-icon {
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.empty-state h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

/* Pagination */
.pagination-nav {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md) 0;
}

.pagination {
  margin: 0;
}

.page-link {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--border-radius-sm);
}

.page-link:hover {
  color: var(--primary);
  background-color: var(--bg-secondary);
  border-color: var(--primary);
}

.page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.page-item.disabled .page-link {
  color: var(--text-muted);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

/* Dark mode adjustments for empty state and pagination */
[data-theme="dark"] .empty-state {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .page-link {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .page-link:hover {
  color: var(--primary-light);
  background-color: var(--bg-tertiary);
  border-color: var(--primary);
}

[data-theme="dark"] .page-item.disabled .page-link {
  color: var(--text-muted);
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}
